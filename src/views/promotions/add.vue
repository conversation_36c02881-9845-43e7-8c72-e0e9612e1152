<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Promotions" pageSubtitle="Add Promotion" />

    <div class="block py-4 mx-3 px-5">
      <!-- Basic Information Card -->
      <div class="filter-card p-3 border rounded-md mb-4 hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Basic Information</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Promotion Name</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.promo_name" placeholder="Enter Promotion Name">
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Filter Promotion</label>
            <select class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="promo.promotion_type_id">
              <option value="" disabled>Select Promotion</option>
              <option v-for="item in promotion_filters" :value="item.value">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Select Start & End Date</label>
            <VueDatePicker
              v-model="date"
              range
              multi-calendars
              :enable-time-picker="false"
              :format="'yyyy-MM-dd'"
              :preset-ranges="presetRanges"
              placeholder="Select date range"
              class="w-full text-xs"
              @update:model-value="selectDate"
            />
          </div>
        </div>
      </div>

      <!-- URL and Image Card -->
      <div class="filter-card p-3 border rounded-md mb-4 hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-2">URLs and Images</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Call to Action URL</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.promo_url" placeholder="Enter Promotion URL">
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Image URL</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" name="image" v-model="promo.promo_images" placeholder="Enter Image url" required>
          </div>
        </div>
      </div>

      <!-- Description Card -->
      <div class="filter-card p-3 border rounded-md mb-4 hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Description</h4>
        <div class="block">
          <label class="block text-xs font-bold text-gray-700 mb-1">Promotion Description</label>
          <textarea class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    rows="4" v-model="promo.description" placeholder="Enter Description"></textarea>
        </div>
      </div>

      <!-- Bonus Amount Card -->
      <div class="filter-card p-3 border rounded-md mb-4 hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Bonus Settings</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">

          <!-- switch button -->
          <div class="flex items-center mb-4">
  <label class="inline-flex items-center cursor-pointer">
    <span class="mr-3 text-xs font-bold text-gray-700">Bonus Type</span>
    <div class="relative">
      <input 
        type="checkbox" 
        class="sr-only peer" 
        v-model="promotion_type"
        @change="handleBonusTypeChange(promotion_type)"
      >
      <div class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
    </div>
   <span class="pl-1">  {{ promotion_type ? 'Percentage' : 'Amount' }}</span>
  </label>
</div>
          <!-- Conditional Input Fields -->
<!-- <div v-if="promotion_type" class="block mb-4">
  <label class="block text-xs font-bold text-gray-700 mb-1">Bonus Percentage</label>
  <input 
    class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
    type="number" 
    min="0" 
    max="100"
    step="0.01"
    v-model="promo.bonus_data.bonus_percentage" 
    placeholder="0.00%"
  >
</div> -->
<div class="block mb-4">
  <label class="block text-xs font-bold text-gray-700 mb-1">Bonus Amount</label>
  <input 
    class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
    type="number" 
    min="0"
    step="0.01"
    v-model="promo.bonus_amount" 
    placeholder="Enter Bonus Amount"
  >
</div>
          
         

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Min Odds</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.min_odds" placeholder="Enter Min Odds">
          </div>

          <!-- <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Min Odds Per Pick</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.min_odds_per_pick" placeholder="Enter Min Odds Per Pick">
          </div> -->

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Bet Count</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.bet_count" placeholder="Enter Bet Count">
          </div>
        </div>
      </div>

      <!-- Limits Card -->
      <div class="filter-card p-3 border rounded-md mb-4 hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Limits and Requirements</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Min Stake</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.min_stake" placeholder="Enter Min Stake">
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Min Selections</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.min_selections" placeholder="Enter Min Selections">
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Max Times</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.max_times" placeholder="Enter Max Times">
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Max Win</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text" v-model="promo.bonus_data.max_win" placeholder="Enter Max Win">
          </div>
        </div>
      </div>

      <!-- Settings Card -->
      <div class="filter-card p-3 border rounded-md mb-4 hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Additional Settings</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Frequency</label>
            <select class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="promo.bonus_data.frequency">
              <option value="" disabled>Select Frequency</option>
              <option v-for="item in frequencyOptions" :value="item.value">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Deduct Stake</label>
            <select class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="promo.bonus_data.deduct_stake">
              <option value="" disabled>Select</option>
              <option v-for="item in yesNo" :value="item.value">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Restrict Withdrawal</label>
            <select class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="promo.bonus_data.restrict_withdrawals">
              <option value="" disabled>Select</option>
              <option v-for="item in restrictWithdrawal" :value="item.value">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Allow Duplicate Events</label>
            <select class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="promo.bonus_data.allow_duplicate_events">
              <option value="" disabled>Select</option>
              <option v-for="item in yesNo" :value="item.value">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Status</label>
            <select class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="promo.bonus_data.status">
              <option value="" disabled>Select status</option>
              <option v-for="item in statuses" :value="item.value">
                {{ item.text }}
              </option>
            </select>
          </div>

          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Expiry Period (hrs)</label>
            <input class="w-full block px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                   type="text"
                   v-model="promo.bonus_data.expiry_period"
                   placeholder="Enter Expiry Period">
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex justify-end gap-3 mt-6">
        <router-link class="px-4 py-2 text-xs font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors"
                     :to="{name: 'promotions'}">
          Cancel
        </router-link>

        <button class="px-4 py-2 text-xs font-medium text-white bg-primary rounded-md hover:bg-opacity-90 transition-colors"
                @click="createPromo" id="submit">
          <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading"></vue-loaders-ball-beat>
          <span v-show="!loading">Submit</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import {mapActions} from "vuex";
import moment from "moment-timezone";
import VueDatePicker from "@vuepic/vue-datepicker";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      isLoading: false,
      fullPage: false,
      loading: false,
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      form: {
        timestamp: "254",
        username: '',
        display_name: '',
        role_id: '',
        permissions_acl: [],
      },
      //
      roles: [],
      permissions: [],

      folder: 'images',

      filter: null,
      promotion_type: false,
      //
      promo: {
        timestamp: Date.now(),
        promo_name: "Bonus BiGi",
        promo_url: "https://www.promotions.com",
        promo_details: "D",
        promo_images: "d",
        promotion_type_id: "",
        starting_date: "",
        ending_date: "",
        bonus_data: {
          bonus_type: 1, // 1 = percentage, 2 = amount
          bonus_amount: 30,
          min_odds: 4.99,
          min_odds_per_pick: 4.99,
          bet_count: 2,
          min_stake: 20,
          min_selections: 5,
          max_times: 1,
          max_win: 10000,
          frequency: "once",
          deduct_stake: 1,
          restrict_withdrawal: true,
          allow_duplicate_events: 1,
          allow_duplicate_events_per_ip: 1,
          expiry_period: 48,
          market_conditions: [
            {name: "1X2", value: ""},
            {name: "Both Teams To Score", value: "BTTS"}
          ],
          status: 1,
        }
      },
      // searchDropdown: false,
      // Filter & filterParams
      promotion_filters: [],
      filterParams: {
        timestamp: '',
        promo_name: '',
        component: '',
        type_id: '',
        status: '',
        limit: '',
        sort: '',
        page: '',
        start: '',
        end: '',
      },
      // Yes/no
      yesNo: [
        {text: 'Yes', value: 1},
        {text: 'No', value: 0}
      ],
      // Frequency Options
      frequencyOptions: [
        {text: 'Once', value: 'once'},
        {text: 'Daily', value: 'daily'},
        {text: 'Weekly', value: 'weekly'},
        {text: 'Bi-Weekly', value: 'bi-weekly'},
        {text: 'Monthly', value: 'monthly'},
      ],
      // statuses
      statuses: [
        {text: 'Active', value: 1},
        {text: 'Deactivated', value: 2},
        {text: 'Suspended', value: 3},
      ],
      // restrict Withdrawal
      restrictWithdrawal: [
        {text: 'Yes', value: true},
        {text: 'No', value: false},
      ],
      //

      date: null,
      presetRanges: [
        {label: "All", range: ["", ""]},
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },
  async mounted() {
    await this.selectDate()
    await this.setPromotionFilters()
  },
  methods: {
    ...mapActions(["getPromotionFilters", "createPromotion", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    handleBonusTypeChange(type) {
      this.promo.bonus_data.bonus_type = type ? 1 : 2
    },

    //
    async createPromo() {
      let app = this
      let payload = app.promo;

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this add this file!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.createPromotion(payload)
        },
      })
          .then(async (result) => {
            console.log(JSON.stringify(result.value))
            if (result.value.status === 201) {
              app.link = result.value.message
              app.$swal.fire({
                title: 'Added!',
                text: result.value.message,
                icon: 'success',
                confirmButtonColor: '#26a528',
                confirmButtonText: 'OK'
              });
            } else {
              app.$swal.fire({
                title: 'Error!',
                text: result.value.message,
                icon: 'error',
                confirmButtonColor: '#26a528',
                confirmButtonText: 'OK'
              });
            }
          })
    },


    // Fetch Promotion Filters
    async setPromotionFilters() {
      let app = this
      app.isLoading = true
      app.filterParams.timestamp = Date.now()
      let response = await this.getPromotionFilters(app.filterParams)
      let list=[]
      if (response.status === 200) {
        for (let i = 0; i < response.message.result.length; i++) {
          let item = response.message.result[i];
          list.push({text: item.type, value: parseInt(item.id)});
        }
        app.promotion_filters = list
      }
      app.isLoading = false
    },

    //
    async selectDate() {
      // If date is null (cleared), reset date filters to current date
      if (!this.date) {
        console.log('Date filter cleared, resetting to current date...');
        this.promo.starting_date = this.formatDate(Date.now());
        this.promo.ending_date = this.formatDate(Date.now());
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.promo.starting_date = this.formatDate(this.date[0]);
      this.promo.ending_date = this.formatDate(this.date[1]);

      // Log that date filter was updated
      console.log('Date range updated:', this.promo.starting_date, 'to', this.promo.ending_date);
    },

    // format date
    formatDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

  },

}
</script>

<style scoped>
/* Add these styles to match the deposit table filter styles */
:deep(.dp__main) {
  font-size: 0.75rem;
}

:deep(.dp__input) {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Add filter card styling */
.filter-card {
  background-color: white;
  transition: all 0.2s ease;
}

.filter-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Make buttons more consistent with deposit table */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Add badge styling for consistency */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
}



/* Add these styles for the date picker */
:deep(.dp__main) {
  font-size: 0.75rem;
}

:deep(.dp__input) {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  width: 100%;
}

:deep(.dp__input:hover) {
  border-color: #a5b4fc;
}

:deep(.dp__input:focus) {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.2);
}

:deep(.dp__preset_ranges) {
  font-size: 0.75rem;
}

:deep(.dp__action_buttons) {
  font-size: 0.75rem;
}
</style>
